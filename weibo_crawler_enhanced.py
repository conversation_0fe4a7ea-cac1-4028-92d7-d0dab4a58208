#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪微博评论爬虫脚本 - 增强版
使用webdriver-manager自动管理ChromeDriver

"""

import time
import csv
import json
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import (
    NoSuchElementException, 
    TimeoutException, 
    ElementClickInterceptedException,
    StaleElementReferenceException
)
from config import *

class WeiboCommentCrawlerEnhanced:
    def __init__(self):
        """初始化爬虫"""
        self.driver = None
        self.wait = None
        self.comments_data = []
        
    def setup_driver(self):
        """配置并启动WebDriver - 桌面版模式"""
        print("正在启动浏览器（桌面版模式）...")

        try:
            # Chrome选项配置
            chrome_options = Options()

            # 基础配置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument(f'--user-agent={USER_AGENT}')

            # 使用webdriver-manager自动下载和管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # 隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.wait = WebDriverWait(self.driver, 10)
            print("✓ 浏览器启动成功（桌面版模式）！")
            return True
            
        except Exception as e:
            print(f"浏览器启动失败: {e}")
            print("请确保已安装Chrome浏览器")
            return False
    
    def get_page_url(self, page_num):
        """生成指定页码的URL"""
        base_url = TARGET_URL.replace("&page=1", f"&page={page_num}")
        if "&page=" not in base_url:
            base_url += f"&page={page_num}"
        return base_url

    def manual_login(self):
        """引导用户手动登录微博 - 桌面版"""
        print("\n=== 微博登录（桌面版）===")
        print("正在打开桌面版微博登录页面...")

        try:
            self.driver.get("https://weibo.com")
            time.sleep(3)

            print("\n请按照以下步骤手动登录微博：")
            print("1. 在打开的浏览器窗口中点击登录")
            print("2. 使用扫码或账号密码方式登录")
            print("3. 登录成功后，请回到此控制台")
            print("4. 按回车键继续执行爬取任务...")

            input("\n登录完成后请按回车键继续...")

            time.sleep(2)
            if self.check_login_status():
                print("✓ 登录验证成功！")
                return True
            else:
                print("✗ 登录验证失败，请重新登录")
                return False

        except Exception as e:
            print(f"登录过程出错: {e}")
            return False

    def check_login_status(self):
        """检查登录状态"""
        try:
            # 检查多个可能的登录状态指示元素
            login_indicators = [
                ".gn_name", ".name", "[class*='name']",
                ".Avatar", "[class*='avatar']",
                ".UserInfo", "[class*='userinfo']"
            ]

            for selector in login_indicators:
                try:
                    self.driver.find_element(By.CSS_SELECTOR, selector)
                    return True
                except NoSuchElementException:
                    continue
            return False
        except:
            return False

    def crawl_user_page(self):
        """爬取用户页面数据"""
        print(f"\n=== 开始爬取用户页面数据（最多{WEIBO_COUNT}条微博）===")

        # 获取当前页面的微博列表
        weibo_list = self.get_weibo_list(WEIBO_COUNT)
        if not weibo_list:
            print("未找到微博，停止爬取")
            return

        print(f"找到 {len(weibo_list)} 条微博")

        # 爬取当前页面的评论
        total_comments = 0
        for i, weibo_element in enumerate(weibo_list):
            try:
                print(f"\n处理第 {i + 1} 条微博")

                # 记录当前用户页面URL，用于返回
                user_page_url = self.driver.current_url

                comments = self.extract_comments_from_weibo(weibo_element, i, MAX_COMMENTS_PER_WEIBO)
                self.comments_data.extend(comments)
                total_comments += len(comments)

                print(f"当前微博获取评论: {len(comments)} 条")
                print(f"累计评论总数: {len(self.comments_data)} 条")

                # 如果已经获取足够的评论，可以提前结束
                if len(self.comments_data) >= TARGET_TOTAL_COMMENTS:
                    print(f"已获取 {len(self.comments_data)} 条评论，达到目标数量")
                    return

                # 确保返回到正确的用户页面准备处理下一条微博
                if i < len(weibo_list) - 1:  # 不是最后一条
                    print("确保在用户页面...")

                    # 检查当前URL是否是用户页面
                    current_url = self.driver.current_url
                    if user_page_url not in current_url and "weibo.com/u/" not in current_url:
                        print(f"当前页面不是用户页面，直接访问: {user_page_url}")
                        self.driver.get(user_page_url)
                        time.sleep(3)

                        # 重新获取微博列表（因为页面刷新了）
                        weibo_list = self.get_weibo_list(WEIBO_COUNT)
                        if i + 1 >= len(weibo_list):
                            print("微博列表长度不足，停止爬取")
                            break

            except Exception as e:
                print(f"处理第 {i + 1} 条微博时出错: {e}")
                # 发生错误时也要确保返回用户页面
                try:
                    if 'user_page_url' in locals():
                        self.driver.get(user_page_url)
                        time.sleep(2)
                except:
                    pass
                continue

        print(f"用户页面爬取完成，获取评论 {total_comments} 条")

    
    def get_weibo_list(self, target_count=WEIBO_COUNT):
        """获取指定数量的微博 - 基于network.md的HTML结构优化"""
        print(f"\n=== 获取前{target_count}条微博 ===")
        weibo_elements = []

        try:
            scroll_count = 0
            max_scrolls = 15  # 增加滚动次数
            last_count = 0
            no_change_count = 0

            while len(weibo_elements) < target_count and scroll_count < max_scrolls:
                # 使用配置文件中的选择器查找微博元素
                current_weibos = []
                for selector in SELECTORS["weibo_cards"]:
                    try:
                        found_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if found_elements:
                            current_weibos.extend(found_elements)
                    except Exception as e:
                        continue

                # 去重并过滤有效的微博元素
                unique_weibos = []
                for weibo in current_weibos:
                    try:
                        if weibo.is_displayed() and weibo not in unique_weibos:
                            # 检查是否包含文本内容
                            if weibo.text.strip():
                                unique_weibos.append(weibo)
                    except:
                        continue

                weibo_elements = unique_weibos[:target_count]
                current_count = len(weibo_elements)
                print(f"当前找到 {current_count} 条有效微博")

                # 检查是否有新增微博
                if current_count == last_count:
                    no_change_count += 1
                    if no_change_count >= 3:  # 连续3次没有新增，停止滚动
                        print("连续多次滚动未发现新微博，停止加载")
                        break
                else:
                    no_change_count = 0
                    last_count = current_count

                if len(weibo_elements) >= target_count:
                    break

                # 滚动加载更多
                print(f"滚动加载更多微博... ({scroll_count + 1}/{max_scrolls})")
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(random.uniform(MIN_DELAY + 1, MAX_DELAY + 2))  # 增加等待时间
                scroll_count += 1

            print(f"✓ 成功获取 {len(weibo_elements)} 条微博")
            return weibo_elements[:target_count]

        except Exception as e:
            print(f"获取微博列表出错: {e}")
            return []
    
    def random_delay(self, min_seconds=MIN_DELAY, max_seconds=MAX_DELAY):
        """随机延时"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def extract_weibo_text(self, weibo_element, weibo_index):
        """提取微博文本内容 - 基于network.md的HTML结构优化"""
        try:
            # 使用配置文件中的选择器提取微博内容
            for selector in SELECTORS["weibo_content"]:
                try:
                    content_element = weibo_element.find_element(By.CSS_SELECTOR, selector)
                    content = content_element.text.strip()
                    if content and len(content) > 10:  # 确保内容有意义
                        # 清理内容，移除多余的空白字符和表情符号
                        content = ' '.join(content.split())
                        # 移除链接文本和展开按钮文本
                        import re
                        content = re.sub(r'http[s]?://\S+', '', content)
                        content = re.sub(r'展开.*?', '', content)
                        content = re.sub(r'收起.*?', '', content)
                        content = content.strip()

                        if len(content) > 20:  # 确保清理后仍有有效内容
                            return content[:200] + "..." if len(content) > 200 else content
                except Exception as e:
                    continue

            # 尝试提取用户昵称作为备用标识
            try:
                for selector in SELECTORS["user_nicknames"]:
                    try:
                        nickname_element = weibo_element.find_element(By.CSS_SELECTOR, selector)
                        nickname = nickname_element.text.strip()
                        if nickname:
                            return f"{nickname}的微博_{weibo_index + 1}"
                    except:
                        continue
            except:
                pass

            # 如果都失败了，尝试获取整个元素的文本
            full_text = weibo_element.text.strip()
            if full_text:
                lines = full_text.split('\n')
                for line in lines:
                    if len(line.strip()) > 20:  # 找到较长的文本行
                        return line.strip()[:200] + "..." if len(line.strip()) > 200 else line.strip()

            return f"微博_{weibo_index + 1}"

        except Exception as e:
            print(f"提取微博文本出错: {e}")
            return f"微博_{weibo_index + 1}"
    
    def click_comment_button(self, weibo_element):
        """点击评论按钮 - 桌面版优化"""
        try:
            # 使用配置文件中的评论按钮选择器
            comment_button = None
            for selector in SELECTORS["comment_buttons"]:
                try:
                    comment_button = weibo_element.find_element(By.CSS_SELECTOR, selector)
                    print(f"找到评论按钮: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if comment_button:
                try:
                    # 滚动到元素可见
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", comment_button)
                    time.sleep(1)

                    # 桌面版点击事件
                    comment_button.click()
                    print("✓ 成功点击评论按钮（桌面版）")
                    return True

                except ElementClickInterceptedException:
                    # 如果点击被拦截，尝试用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", comment_button)
                    print("✓ 使用JavaScript点击评论按钮")
                    return True
                except Exception as click_error:
                    print(f"点击评论按钮失败: {click_error}")
                    return False
            else:
                print("✗ 未找到评论按钮")
                return False
                
        except Exception as e:
            print(f"点击评论按钮出错: {e}")
            return False

    def click_back_button(self):
        """点击返回按钮 - 基于hit.md的HTML结构"""
        try:
            # 使用配置文件中的返回按钮选择器
            back_button = None
            for selector in SELECTORS["back_buttons"]:
                try:
                    back_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"找到返回按钮: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if back_button:
                try:
                    # 滚动到元素可见
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", back_button)
                    time.sleep(1)

                    # 点击返回按钮
                    back_button.click()
                    print("✓ 成功点击返回按钮")
                    return True

                except ElementClickInterceptedException:
                    # 如果点击被拦截，尝试用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", back_button)
                    print("✓ 使用JavaScript点击返回按钮")
                    return True
                except Exception as click_error:
                    print(f"点击返回按钮失败: {click_error}")
                    return False
            else:
                print("✗ 未找到返回按钮")
                return False

        except Exception as e:
            print(f"点击返回按钮出错: {e}")
            return False

    def extract_comments_from_weibo(self, weibo_element, weibo_index, max_comments=MAX_COMMENTS_PER_WEIBO):
        """从单条微博提取评论"""
        print(f"\n--- 处理第 {weibo_index + 1} 条微博 ---")
        comments = []

        try:
            # 提取微博内容
            weibo_text = self.extract_weibo_text(weibo_element, weibo_index)
            print(f"微博内容: {weibo_text}")

            # 点击评论按钮
            if self.click_comment_button(weibo_element):
                self.random_delay(LOAD_MORE_DELAY, LOAD_MORE_DELAY + 2)
                comments = self.extract_comments_from_page(weibo_text, max_comments)

                # 爬取完评论后，点击返回按钮回到主页面
                if self.click_back_button():
                    print("✓ 成功返回主页面")
                    self.random_delay(MIN_DELAY, MAX_DELAY)
                else:
                    print("⚠️ 返回按钮点击失败，尝试浏览器后退")
                    self.driver.back()
                    self.random_delay(MIN_DELAY, MAX_DELAY)
            else:
                print("无法访问评论区")

        except Exception as e:
            print(f"处理微博出错: {e}")

        return comments

    def extract_comments_from_page(self, weibo_text, max_comments):
        """从当前页面提取评论"""
        comments = []
        load_more_attempts = 0
        max_load_attempts = 10

        try:
            # 记录当前窗口句柄，用于后续返回
            original_window = self.driver.current_window_handle

            while len(comments) < max_comments and load_more_attempts < max_load_attempts:
                # 使用配置文件中的评论列表选择器
                current_comments = []
                for selector in SELECTORS["comment_lists"]:
                    try:
                        current_comments = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if current_comments:
                            break
                    except:
                        continue

                if current_comments:
                    print(f"找到 {len(current_comments)} 条评论")

                    for comment_element in current_comments:
                        if len(comments) >= max_comments:
                            break

                        try:
                            comment_data = self.extract_single_comment(comment_element, weibo_text)
                            if comment_data and comment_data not in comments:
                                comments.append(comment_data)
                        except StaleElementReferenceException:
                            continue
                        except Exception as e:
                            print(f"提取单条评论出错: {e}")
                            continue

                # 尝试加载更多评论
                if len(comments) < max_comments:
                    if not self.load_more_comments():
                        break
                    load_more_attempts += 1
                    self.random_delay(MIN_DELAY, MAX_DELAY)
                else:
                    break

            print(f"✓ 从当前页面提取到 {len(comments)} 条评论")

            # 检查是否有"更多评论"链接，如果有则在新标签页中打开
            if len(comments) < max_comments:
                more_comments = self.extract_more_comments_in_new_tab(weibo_text, max_comments - len(comments), original_window)
                comments.extend(more_comments)
                print(f"✓ 从更多评论页面额外获取到 {len(more_comments)} 条评论")

        except Exception as e:
            print(f"提取评论过程出错: {e}")

        return comments

    def extract_single_comment(self, comment_element, weibo_text):
        """提取单条评论的信息"""
        try:
            # 提取评论者昵称
            nickname = "未知用户"
            for selector in SELECTORS["nicknames"]:
                try:
                    nickname_element = comment_element.find_element(By.CSS_SELECTOR, selector)
                    nickname = nickname_element.text.strip()
                    if nickname and nickname != ":":  # 过滤无效昵称
                        break
                except:
                    continue

            # 提取评论内容 - 特殊处理详情页面结构
            content = "无内容"

            # 首先尝试详情页面的特殊结构
            try:
                # 查找 .text 元素，然后提取其中的 span 内容
                text_element = comment_element.find_element(By.CSS_SELECTOR, ".text")
                if text_element:
                    # 获取完整文本
                    full_text = text_element.text.strip()
                    if full_text and ":" in full_text:
                        # 分割用户名和评论内容 (格式: "用户名:评论内容")
                        parts = full_text.split(":", 1)
                        if len(parts) > 1:
                            content = parts[1].strip()
                            # 如果昵称还是默认值，尝试从这里提取
                            if nickname == "未知用户" and parts[0].strip():
                                nickname = parts[0].strip()
            except:
                pass

            # 如果上面的方法没有成功，使用通用选择器
            if content == "无内容":
                for selector in SELECTORS["contents"]:
                    try:
                        content_element = comment_element.find_element(By.CSS_SELECTOR, selector)
                        content = content_element.text.strip()
                        if content and content != nickname and ":" not in content:
                            break
                    except:
                        continue

            # 提取评论时间
            comment_time = "未知时间"
            for selector in SELECTORS["times"]:
                try:
                    time_element = comment_element.find_element(By.CSS_SELECTOR, selector)
                    time_text = time_element.text.strip()
                    # 提取时间部分（通常在开头）
                    if time_text:
                        # 分割并取第一部分作为时间
                        time_parts = time_text.split()
                        if time_parts:
                            # 查找包含日期格式的部分
                            for part in time_parts:
                                if "-" in part or ":" in part or "月" in part or "日" in part:
                                    comment_time = part
                                    break
                            if comment_time == "未知时间" and time_parts[0]:
                                comment_time = time_parts[0]
                        break
                except:
                    continue

            # 数据清理
            if content and content != "无内容":
                # 移除多余的空白字符
                content = ' '.join(content.split())
                # 移除可能的用户名前缀
                if content.startswith(nickname + ":"):
                    content = content[len(nickname + ":"):].strip()

            return {
                "微博内容": weibo_text,
                "评论者昵称": nickname,
                "评论内容": content,
                "评论时间": comment_time,
                "爬取时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            print(f"提取单条评论信息出错: {e}")
            return None

    def extract_more_comments_in_new_tab(self, weibo_text, max_comments, original_window):
        """在新标签页中提取更多评论"""
        more_comments = []

        try:
            # 查找"更多评论"链接
            more_comments_link = self.find_more_comments_link()
            if not more_comments_link:
                print("未找到更多评论链接")
                return more_comments

            # 获取链接URL
            link_url = more_comments_link.get_attribute('href')
            if not link_url:
                print("更多评论链接无效")
                return more_comments

            print(f"找到更多评论链接: {link_url}")

            # 在新标签页中打开链接
            self.driver.execute_script("window.open(arguments[0]);", link_url)

            # 切换到新标签页
            all_windows = self.driver.window_handles
            new_window = None
            for window in all_windows:
                if window != original_window:
                    new_window = window
                    break

            if new_window:
                self.driver.switch_to.window(new_window)
                time.sleep(5)  # 等待页面加载

                print("已切换到更多评论页面，开始提取评论...")

                # 在新页面中提取评论
                more_comments = self.extract_comments_from_detail_page(weibo_text, max_comments)

                # 关闭新标签页并返回原窗口
                self.driver.close()
                self.driver.switch_to.window(original_window)
                print("已关闭更多评论页面，返回原页面")

        except Exception as e:
            print(f"在新标签页中提取更多评论出错: {e}")
            # 确保返回原窗口
            try:
                self.driver.switch_to.window(original_window)
            except:
                pass

        return more_comments

    def find_more_comments_link(self):
        """查找更多评论链接"""
        try:
            # 使用配置文件中的更多评论选择器
            for selector in SELECTORS["more_comments"]:
                try:
                    # 特殊处理包含文本的选择器
                    if ":contains(" in selector:
                        # 使用XPath来查找包含特定文本的元素
                        text_to_find = selector.split(":contains('")[1].split("')")[0]
                        xpath = f"//a[contains(text(), '{text_to_find}')]"
                        elements = self.driver.find_elements(By.XPATH, xpath)
                        if elements:
                            return elements[0]
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if element and element.is_displayed():
                            return element
                except:
                    continue

            # 额外尝试查找包含特定文本的链接
            try:
                # 查找包含"后面还有"和"条评论，点击查看"的链接
                xpath_patterns = [
                    "//a[contains(text(), '后面还有') and contains(text(), '条评论，点击查看')]",
                    "//a[contains(text(), '条评论，点击查看')]",
                    "//a[contains(text(), '后面还有')]",
                    "//a[contains(@href, 'weibo.com') and contains(text(), '评论')]"
                ]

                for xpath in xpath_patterns:
                    elements = self.driver.find_elements(By.XPATH, xpath)
                    if elements:
                        return elements[0]

            except Exception as e:
                print(f"XPath查找更多评论链接出错: {e}")

            return None

        except Exception as e:
            print(f"查找更多评论链接出错: {e}")
            return None

    def extract_comments_from_detail_page(self, weibo_text, max_comments):
        """从微博详情页面提取评论 - 支持Vue虚拟滚动"""
        comments = []
        load_more_attempts = 0
        max_load_attempts = 20  # 详情页可能有更多评论，增加尝试次数
        last_comment_count = 0
        no_new_comments_count = 0

        try:
            # 等待页面加载完成
            time.sleep(3)

            while len(comments) < max_comments and load_more_attempts < max_load_attempts:
                # 使用配置文件中的评论列表选择器
                current_comments = []
                for selector in SELECTORS["comment_lists"]:
                    try:
                        found_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if found_elements:
                            current_comments.extend(found_elements)
                    except:
                        continue

                # 去重
                unique_comments = []
                for comment in current_comments:
                    try:
                        if comment.is_displayed() and comment not in unique_comments:
                            unique_comments.append(comment)
                    except:
                        continue

                if unique_comments:
                    print(f"详情页找到 {len(unique_comments)} 条评论")

                    for comment_element in unique_comments:
                        if len(comments) >= max_comments:
                            break

                        try:
                            comment_data = self.extract_single_comment(comment_element, weibo_text)
                            if comment_data and self.is_valid_comment(comment_data) and comment_data not in comments:
                                comments.append(comment_data)
                        except StaleElementReferenceException:
                            continue
                        except Exception as e:
                            print(f"提取详情页单条评论出错: {e}")
                            continue

                # 检查是否有新评论
                if len(comments) == last_comment_count:
                    no_new_comments_count += 1
                    if no_new_comments_count >= 3:  # 连续3次没有新评论就停止
                        print("连续多次没有新评论，停止加载")
                        break
                else:
                    no_new_comments_count = 0
                    last_comment_count = len(comments)

                # 尝试加载更多评论 - 对于Vue虚拟滚动，主要通过滚动
                if len(comments) < max_comments:
                    # 滚动到页面底部以触发虚拟滚动加载更多内容
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(2)

                    # 尝试点击加载更多按钮
                    if not self.load_more_comments():
                        # 如果没有加载更多按钮，继续滚动
                        for _ in range(3):
                            self.driver.execute_script("window.scrollBy(0, 500);")
                            time.sleep(1)

                    load_more_attempts += 1
                    self.random_delay(MIN_DELAY, MAX_DELAY)
                else:
                    break

            print(f"✓ 从详情页提取到 {len(comments)} 条评论")

        except Exception as e:
            print(f"从详情页提取评论出错: {e}")

        return comments

    def is_valid_comment(self, comment_data):
        """验证评论数据是否有效"""
        if not comment_data:
            return False

        nickname = comment_data.get("评论者昵称", "")
        content = comment_data.get("评论内容", "")

        # 过滤无效数据
        if nickname in ["未知用户", "", ":"]:
            return False
        if content in ["无内容", "", ":"]:
            return False
        if len(content) < 2:  # 评论内容太短
            return False

        return True

    def load_more_comments(self):
        """加载更多评论"""
        try:
            # 使用配置文件中的加载更多按钮选择器
            for selector in SELECTORS["load_more"]:
                try:
                    load_more_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if load_more_button.is_displayed():
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", load_more_button)
                        time.sleep(1)
                        load_more_button.click()
                        print("✓ 点击加载更多评论")
                        return True
                except:
                    continue

            # 如果没有找到加载更多按钮，尝试滚动
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            return False

        except Exception as e:
            print(f"加载更多评论出错: {e}")
            return False

    def save_to_csv(self, filename=None):
        """保存数据到CSV文件"""
        if filename is None:
            filename = OUTPUT_FILES["csv"]

        if not self.comments_data:
            print("没有数据需要保存")
            return

        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=CSV_COLUMNS)
                writer.writeheader()
                for comment in self.comments_data:
                    writer.writerow(comment)

            print(f"✓ 数据已保存到 {filename}")
            print(f"✓ 共保存 {len(self.comments_data)} 条评论")

        except Exception as e:
            print(f"保存CSV文件出错: {e}")

    def save_to_json(self, filename=None):
        """保存数据到JSON文件"""
        if filename is None:
            filename = OUTPUT_FILES["json"]

        if not self.comments_data:
            print("没有数据需要保存")
            return

        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.comments_data, jsonfile, ensure_ascii=False, indent=2)

            print(f"✓ 数据已保存到 {filename}")

        except Exception as e:
            print(f"保存JSON文件出错: {e}")

    def run_crawler(self):
        """运行爬虫主程序 - 用户页面爬取"""
        print("=== 微博用户页面评论爬虫启动 ===")
        print("目标: 爬取新京报用户页面'重庆万州公交'相关微博评论")
        print(f"预期获取: 约{TARGET_TOTAL_COMMENTS}条评论数据")
        print(f"目标微博数: 最多{WEIBO_COUNT}条")
        print(f"每条微博最大评论数: {MAX_COMMENTS_PER_WEIBO}条")
        print(f"目标网站: {TARGET_URL}\n")

        try:
            # 1. 启动浏览器
            if not self.setup_driver():
                return False

            # 2. 手动登录
            if not self.manual_login():
                print("登录失败，程序退出")
                return False

            # 3. 访问目标用户页面
            print("=== 访问目标用户页面 ===")
            print("正在访问用户微博页面...")
            self.driver.get(TARGET_URL)
            time.sleep(8)  # 等待页面加载

            print(f"页面标题: {self.driver.title}")
            print(f"当前URL: {self.driver.current_url}")

            # 检查页面是否正常加载
            if "新京报" in self.driver.title or "微博" in self.driver.title or "weibo.com" in self.driver.current_url:
                print("✓ 目标页面加载成功！")
            else:
                print("⚠️ 页面标题异常，但继续尝试爬取...")

            # 4. 爬取用户页面数据
            self.crawl_user_page()

            # 6. 保存数据
            print(f"\n=== 爬取完成 ===")
            print(f"总共获取评论: {len(self.comments_data)} 条")

            if self.comments_data:
                self.save_to_csv()
                self.save_to_json()
                print("✓ 数据保存完成")

                # 显示统计信息
                self.show_statistics()
            else:
                print("✗ 未获取到任何评论数据")

            return True

        except KeyboardInterrupt:
            print("\n用户中断程序")
            return False
        except Exception as e:
            print(f"程序运行出错: {e}")
            return False
        finally:
            # 关闭浏览器
            if self.driver:
                print("正在关闭浏览器...")
                self.driver.quit()
                print("✓ 浏览器已关闭")

    def show_statistics(self):
        """显示爬取统计信息"""
        if not self.comments_data:
            return

        print(f"\n=== 爬取统计信息 ===")
        print(f"总评论数: {len(self.comments_data)}")

        # 统计不同微博的评论数
        weibo_stats = {}
        for comment in self.comments_data:
            weibo_content = comment["微博内容"]
            if weibo_content in weibo_stats:
                weibo_stats[weibo_content] += 1
            else:
                weibo_stats[weibo_content] = 1

        print(f"涉及微博数: {len(weibo_stats)}")
        print(f"平均每条微博评论数: {len(self.comments_data) / len(weibo_stats):.1f}")

        # 显示评论数最多的前3条微博
        sorted_weibos = sorted(weibo_stats.items(), key=lambda x: x[1], reverse=True)
        print(f"\n评论数最多的微博:")
        for i, (weibo, count) in enumerate(sorted_weibos[:3]):
            print(f"{i+1}. {weibo} ({count}条评论)")

def main():
    """主函数"""
    crawler = WeiboCommentCrawlerEnhanced()

    try:
        success = crawler.run_crawler()
        if success:
            print("\n🎉 爬虫任务完成！")
            print("数据文件已保存在当前目录下")
        else:
            print("\n❌ 爬虫任务失败")
    except Exception as e:
        print(f"程序异常退出: {e}")

if __name__ == "__main__":
    main()
